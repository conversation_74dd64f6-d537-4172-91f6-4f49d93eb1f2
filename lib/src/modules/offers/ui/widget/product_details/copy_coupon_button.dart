import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/custom_snack_bar.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../models/product.dart';
import '../../../controllers/products_details_controller.dart';

enum CouponButtonState { copy, copied }

class CopyCouponButton extends StatefulWidget {
  final ProductDetailsController controller;
  final Product product;

  const CopyCouponButton({
    super.key,
    required this.controller,
    required this.product,
  });

  @override
  State<CopyCouponButton> createState() => _CopyCouponButtonState();
}

class _CopyCouponButtonState extends State<CopyCouponButton> {
  CouponButtonState buttonState = CouponButtonState.copy;

  void _handleCopyClick() {
    if (buttonState == CouponButtonState.copy) {
      CustomSnackBar.show(
        justTheBottom: true,
        context: context,
        message:
            'Cupom copiado. Vá para a loja e aproveite seu cupom.',
        icon: SvgIcons.feedbackCheck,
      );
    }

    setState(() {
      buttonState = CouponButtonState.copied;
    });

    widget.controller.copyCoupon(widget.product.cupom);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 6),
          child: DottedBorder(
            borderType: BorderType.RRect,
            padding: EdgeInsets.all(1),
            radius: Radius.circular(16),
            dashPattern: [3, 3],
            color: ColorOutlet.systemBorderDisabled,
            child: InkWell(
              splashColor: ColorOutlet.systemBorderDisabled
                  .withValues(alpha: 0.3),
              highlightColor: ColorOutlet.systemBorderDisabled
                  .withValues(alpha: 0.3),
              onTap: _handleCopyClick,
              borderRadius: BorderRadius.circular(16),
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                width: double.infinity,
                child: Row(
                  spacing: 4,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextPattern.customText(
                      text: widget.product.cupom,
                      fontSize: 16,
                      color:
                          widget.product.invalidProduct
                              ? ColorOutlet.contentGhost
                              : ColorOutlet.contentPrimary,
                      fontWeightOption: FontWeightOption.bold,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    Row(
                      spacing: 8,
                      children: [
                        TextPattern.customText(
                          text:
                              buttonState == CouponButtonState.copied
                                  ? 'Copiado'
                                  : 'Copiar',
                          fontSize: 14,
                        ),
                        buttonState == CouponButtonState.copied
                            ? SvgPicture.asset(
                              SvgIcons.feedbackCheckFilled,
                            )
                            : SvgPicture.asset(SvgIcons.actionCopy),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 14,
              color: ColorOutlet.paper,
              padding: EdgeInsets.symmetric(horizontal: 8),
              alignment: Alignment.center,
              child: TextPattern.customText(
                text: 'CUPOM DE DESCONTO',
                fontSize: 11,
                color: ColorOutlet.contentGhost,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
