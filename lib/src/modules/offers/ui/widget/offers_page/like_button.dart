import '../../../../../../theme/color_outlet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../../theme/svg_icons.dart';

class LikeButton extends StatefulWidget {
  final bool isLiked; // Indica se está curtido
  final VoidCallback onTap; // Função chamada ao clicar no botão
  final bool bigIcon;

  const LikeButton({super.key, required this.isLiked, required this.onTap, this.bigIcon = false});

  @override
  State<LikeButton> createState() => _LikeButtonState();
}

class _LikeButtonState extends State<LikeButton> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: const Duration(milliseconds: 300));

    _scaleAnimation = TweenSequence<double>([TweenSequenceItem(tween: Tween(begin: 1.0, end: 1.2).chain(CurveTween(curve: Curves.easeOut)), weight: 50), TweenSequenceItem(tween: Tween(begin: 1.2, end: 1.0).chain(CurveTween(curve: Curves.easeIn)), weight: 50)]).animate(_controller);
  }

  @override
  void didUpdateWidget(covariant LikeButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Executa animação apenas quando o estado de curtida muda
    if (widget.isLiked != oldWidget.isLiked) {
      _controller.forward(from: 0.0);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap, // A lógica do debounce está na página principal
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: SvgPicture.asset(widget.isLiked ? SvgIcons.markerFavoriteFilled : SvgIcons.markerFavorite, colorFilter: ColorFilter.mode(!widget.isLiked ? ColorOutlet.contentSecondary : const Color(0xFFFF4060), BlendMode.srcIn), height: widget.bigIcon ? 32 : 24),
          );
        },
      ),
    );
  }
}
