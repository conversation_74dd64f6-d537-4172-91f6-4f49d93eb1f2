import 'package:flutter/material.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/text_pattern.dart';
import 'dotted_line.dart';

class CardNotificationStorage extends StatelessWidget {
  const CardNotificationStorage({super.key, required this.title, required this.body, required this.date, required this.read});

  final String title;
  final String body;
  final String date;
  final int read;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(color: ColorOutlet.paper),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 5),
                child: Container(width: 8, height: 8, decoration: BoxDecoration(color: getRead(read) ? ColorOutlet.contentGhost : ColorOutlet.contentPrimary, borderRadius: BorderRadius.circular(4))),
              ),
              const SizedBox(width: 16),
              TextPattern.customText(text: formatDateTimeNotification(date), color: ColorOutlet.contentGhost, fontSize: 12),
            ],
          ),
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.only(left: 24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(width: MediaQuery.of(context).size.width - 48, child: TextPattern.customText(text: title, fontSize: 16, maxLines: 2, softWrap: true, overflow: TextOverflow.ellipsis, fontWeightOption: FontWeightOption.bold)),
                const SizedBox(height: 8),
                Padding(padding: const EdgeInsets.only(right: 12), child: TextPattern.customText(text: body.trim(), fontSize: 14)),
              ],
            ),
          ),
          const SizedBox(height: 24),
          const DottedLine(),
        ],
      ),
    );
  }

  String formatDateTimeNotification(String date) {
    DateTime dateTime = DateTime.parse(date);
    DateTime now = DateTime.now();
    if (dateTime.day == now.day && dateTime.month == now.month && dateTime.year == now.year) {
      String formattedMinute = dateTime.minute.toString().padLeft(2, '0');
      return '${dateTime.hour}:$formattedMinute';
    } else if (dateTime.day == now.day - 1 && dateTime.month == now.month && dateTime.year == now.year) {
      return 'Ontem';
    } else {
      return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';
    }
  }

  bool getRead(int read) {
    if (read == 0) {
      return false;
    } else {
      return true;
    }
  }
}
