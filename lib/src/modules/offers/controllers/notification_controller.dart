import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:promobell/src/models/convert_model_notification.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../models/notification_storage_service.dart';
import '../../../models/store_notifications_model.dart';
import '../../../services/sqflite.dart/init_sqflite.dart';
import '../../../services/supabase/db/db.dart';
import '../../../services/supabase/notification/get/get_notification.dart';

class NotificationController with ChangeNotifier {
  InitSqflite sqflite = InitSqflite();
  GetNotification api = GetNotification();

  ValueNotifier<List<NotificationStorageService>> notifications = ValueNotifier<List<NotificationStorageService>>([]);
  ValueNotifier<int> numberNotificationsNotRead = ValueNotifier<int>(0);

  Future<void> getNotifications() async {
    try {
      notifications.value.clear();
      sqflite.getNotifications().then((value) {
        notifications.value = value;
        numberNotificationsNotRead.value = 0;
        for (var notification in notifications.value) {
          if (notification.isRead == 0) {
            numberNotificationsNotRead.value++;
          }
        }
      });
    } catch (e) {
      return;
    }
  }

  Future<void> setAllReadNotification() async {
    try {
      await sqflite.setAllReadNotification();
      await getNotifications();
    } catch (e) {
      return;
    }
  }

  Future<void> setReadNotification(NotificationStorageService notificationStorageService) async {
    try {
      await sqflite.setReadNotification(notificationStorageService);
      await getNotifications();
    } catch (e) {
      return;
    }
  }

  Future<void> deleteNotificationOld() async {
    try {
      await sqflite.deleteNotification();
      await getNotifications();
    } catch (e) {
      return;
    }
  }

  ValueNotifier<bool> isSearch = ValueNotifier<bool>(false);
  void showSeachState(bool value) {
    isSearch.value = value;
  }

  Future<void> observarNotification(Function(StoreNotificationsModel) callback) async {
    final DB tabelaSupabase = DB();
    final SupabaseClient supabase = Supabase.instance.client;
    supabase
        .channel('public:${tabelaSupabase.tabelaDeNotifications}')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: tabelaSupabase.tabelaDeNotifications,
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              final Map<String, dynamic> data = payload.newRecord;
              final StoreNotificationsModel newNotification = StoreNotificationsModel.fromMap(data);
              callback(newNotification);
              addNotification(title: newNotification.title, body: newNotification.message, data: newNotification.date, id: newNotification.id);
            }
          },
        )
        .subscribe();
  }

  Future<void> checkNotification() async {
    final response = await api.getStoreNotifications15days();
    final notifications = await sqflite.getNotifications();
    String? lastOpenedDate;

    final prefs = await SharedPreferences.getInstance();
    lastOpenedDate = prefs.getString('openedDate');

    if (lastOpenedDate != null) {
      DateTime lastOpenedDateTime = DateTime.parse(lastOpenedDate);

      for (var notificationModel in response) {
        final notificationService = notificationModel.toNotificationStorageService();
        if (notificationService.date != null) {
          DateTime notificationDateTime = DateTime.parse(notificationService.date!);
          // Verifica se a data da notificação é posterior à última data de acesso
          if (notificationDateTime.isAfter(lastOpenedDateTime)) {
            bool notificationExists = notifications.any((notificationStorage) => notificationStorage.id == notificationService.id);
            if (!notificationExists) {
              await sqflite.insertNotification(notificationService);
            }
          }
        }
      }
    }

    // Atualiza a última data de acesso para a data atual após um atraso
    Future.delayed(const Duration(seconds: 3), () async {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('openedDate', DateTime.now().toIso8601String());
    });

    notifications.clear();
    numberNotificationsNotRead.value = 0;
    await getNotifications();
  }

  Future<void> addNotification({required String title, required String body, required DateTime data, required int id}) async {
    try {
      String newdate = data.toIso8601String();
      String newdateparse = _parseDateTime(newdate).toString();
      NotificationStorageService newNotification = NotificationStorageService(id: id, title: title, body: body, date: newdateparse, isRead: 0);
      await sqflite.insertNotification(newNotification);
      if (kDebugMode) {
        print('Notification added');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding notification: $e');
      }
    }
  }

  static DateTime _parseDateTime(String dateTimeString) {
    try {
      return DateFormat('yyyy-MM-ddTHH:mm:ss').parse(dateTimeString);
    } catch (e) {
      return DateTime.now();
    }
  }
}
