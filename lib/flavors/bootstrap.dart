// lib/flavors/bootstrap.dart
import 'dart:convert';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_in_app_messaging/firebase_in_app_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:promobell/flavors/firebase/firebase_options.dart' show FirebaseFlavorOptions;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:timezone/data/latest.dart' as tz;

import '../firebase_messaging_service.dart';
import '../src/app/app_module.dart';
import '../src/app/app_widget.dart';
import '../src/services/sqflite.dart/init_sqflite.dart';
import '../supa_base_config_keys.dart';

Future<Map<String, dynamic>> _loadEnvFile(String flavor) async {
  final envFileName = flavor == 'prod' ? '.env' : '.envDesenvolvimento';
  final content = await rootBundle.loadString(envFileName);
  return jsonDecode(content);
}

Future<void> bootstrap(String flavor) async {
  WidgetsFlutterBinding.ensureInitialized();
  try {
    final env = await _loadEnvFile(flavor);

    final supaBaseConfigs = SupaBaseConfigKeys(
    apiUrl: env['API_URL'],
    apiAnonKey: env['API_ANON_KEY'],
    apiServiceRoles: env['API_SERVICE_ROLES'],
    googleWebClientId: env['GOOGLE_WEB_CLIENT_ID'],
    androidClientId: env['ANDROID_CLIENT_ID'],
    iosClientId: env['IOS_CLIENT_ID'],
    gcpProjectId: env['GCP_PROJECT_ID'],
  );

    tz.initializeTimeZones();
    FlutterNativeSplash.remove();
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    // Firebase config baseado no flavor
    FirebaseOptions firebaseOptions;
    switch (flavor) {
      case 'prod':
        firebaseOptions = FirebaseFlavorOptions.prod;
        break;
      case 'dev':
      default:
        firebaseOptions = FirebaseFlavorOptions.dev;
        break;
    }

    await Firebase.initializeApp(options: firebaseOptions);
    final messagingService = FirebaseMessagingService();
    await messagingService.initNotification();
    await messagingService.getInstallationId();
    final fiam = FirebaseInAppMessaging.instance;
    await fiam.setMessagesSuppressed(false);

    await Supabase.initialize(
      url: supaBaseConfigs.apiUrl,
      anonKey: supaBaseConfigs.apiAnonKey,
      realtimeClientOptions: const RealtimeClientOptions(eventsPerSecond: 2),
    );
    await InitSqflite().init();
  } catch (e) {
    rethrow;
  }

  runApp(ModularApp(module: AppModule(), child: const AppWidget()));
  // runApp(
  //   DevicePreview(
  //     enabled: !kReleaseMode,
  //     builder: (context) => ModularApp(module: AppModule(), child: const AppWidget()),
  //   ),
  // );
}
