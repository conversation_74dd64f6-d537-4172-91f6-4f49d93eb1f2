# Correções para Android 15 - Play Store Warnings

Este documento descreve as correções implementadas para resolver os warnings da Play Store relacionados ao Android 15.

## Problemas Identificados

### 1. Suporte a Páginas de 16KB
**Warning:** "Adicionar suporte a 16kb - Recompile seu app para alinhamento à biblioteca nativa de 16 KB"

### 2. Exibição Edge-to-Edge
**Warning:** "Ajustar exibição de ponta a ponta - A exibição de ponta a ponta pode não estar disponível para todos os usuários"

### 3. APIs Descontinuadas
**Warning:** "Ajustar APIs ou parâmetros descontinuados para exibição de ponta a ponta"
- `android.view.Window.setNavigationBarDividerColor`
- `android.view.Window.setStatusBarColor`
- `android.view.Window.setNavigationBarColor`

## Correções Implementadas

### 1. Suporte a Páginas de 16KB

#### android/app/build.gradle
- Adicionado `abiFilters` no `defaultConfig` e `buildTypes.release.ndk`
- Configurado suporte para arquiteturas: `arm64-v8a`, `armeabi-v7a`, `x86_64`

#### android/gradle.properties
- Adicionado `android.experimental.enableNativeLibraryOptimization=true`
- Adicionado `android.experimental.enablePageSizeOptimization=true`

### 2. Edge-to-Edge Moderno

#### MainActivity.kt
- Importado `androidx.activity.enableEdgeToEdge`
- Importado `androidx.core.view.ViewCompat` e `WindowInsetsCompat`
- Substituído configuração manual por `enableEdgeToEdge()`
- Implementado `ViewCompat.setOnApplyWindowInsetsListener` para tratamento de insets
- Removido uso de APIs descontinuadas (`setStatusBarColor`, `setNavigationBarColor`)

#### android/app/build.gradle
- Adicionado dependência `androidx.activity:activity-ktx:1.8.2`

### 3. Migração de APIs Descontinuadas

#### Antes (APIs descontinuadas):
```kotlin
window.statusBarColor = Color.TRANSPARENT
window.navigationBarColor = Color.TRANSPARENT
```

#### Depois (APIs modernas):
```kotlin
enableEdgeToEdge()
WindowCompat.setDecorFitsSystemWindows(window, false)
ViewCompat.setOnApplyWindowInsetsListener(window.decorView) { view, insets ->
    val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
    view.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
    insets
}
```

## Compatibilidade

- **Mínimo SDK:** 23 (mantido)
- **Target SDK:** 35 (Android 15)
- **Compile SDK:** 35 (Android 15)

## Benefícios

1. **16KB Support:** Melhor performance em dispositivos Android 15+ com páginas de memória de 16KB
2. **Edge-to-Edge:** Interface moderna que utiliza toda a tela disponível
3. **APIs Modernas:** Uso de APIs não-descontinuadas, garantindo compatibilidade futura
4. **Backward Compatibility:** Mantém compatibilidade com versões anteriores do Android

## Teste Recomendado

1. Testar em dispositivos Android 15+
2. Verificar comportamento edge-to-edge em diferentes tamanhos de tela
3. Confirmar que as barras de sistema são exibidas corretamente
4. Testar em dispositivos com diferentes configurações de insets (notch, punch-hole, etc.)
