plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id 'com.google.gms.google-services' // Firebase
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode', '1').toInteger()
def flutterVersionName = localProperties.getProperty('flutter.versionName', '1.0.1')

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "br.com.promobell"
    compileSdk = 35
    ndkVersion = flutter.ndkVersion

    flavorDimensions "default"

    productFlavors {
        dev {
            dimension "default"
        }
        prod {
            dimension "default"
        }
    }

    defaultConfig {
        applicationId = "br.com.promobell"
        minSdkVersion 23
        targetSdkVersion 35
        versionCode = flutterVersionCode
        versionName = flutterVersionName
        multiDexEnabled true
        manifestPlaceholders += [
            'appAuthRedirectScheme': applicationId
        ]

        // Configurações para suporte a páginas de 16KB
        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86_64'
        }
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("release")
            minifyEnabled true
            // Desabilitado para evitar FileSystemAlreadyExistsException durante o build
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            ndk {
                debugSymbolLevel 'SYMBOL_TABLE'
                // Configurações específicas para 16KB
                abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86_64'
            }
        }
        debug {
            minifyEnabled false
            shrinkResources false
        }
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
        
        // ✅ Configura o google-services.json correto baseado no flavor
        dev {
            res.srcDirs = ['src/dev']
        }
        prod {
            res.srcDirs = ['src/prod']
        }
    }

    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
    }

    // Configurações para suporte a páginas de 16KB
    bundle {
        language {
            enableSplit = false
        }
        density {
            enableSplit = true
        }
        abi {
            enableSplit = true
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.2'
    implementation 'com.google.android.gms:play-services-location:21.3.0'
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.activity:activity-ktx:1.8.2'
}

// ✅ O google-services.json é selecionado automaticamente pelo sourceSets baseado no flavor