package br.com.promobell

import io.flutter.embedding.android.FlutterActivity
import android.os.Bundle
import android.os.Build
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsControllerCompat
import android.graphics.Color
import android.content.Intent
import android.net.Uri
import io.flutter.plugin.common.MethodChannel
import androidx.activity.enableEdgeToEdge
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat

class MainActivity: FlutterActivity() {
    private val CHANNEL = "br.com.promobell/app_links"
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Habilita edge-to-edge usando a API moderna do Android 15+
        enableEdgeToEdge()

        // Configura o sistema de janelas para edge-to-edge
        WindowCompat.setDecorFitsSystemWindows(window, false)

        // Configura o tratamento de insets do sistema
        ViewCompat.setOnApplyWindowInsetsListener(window.decorView) { view, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            view.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        // Configura a aparência das barras de sistema usando WindowInsetsController
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val controller = WindowInsetsControllerCompat(window, window.decorView)
            controller.isAppearanceLightStatusBars = false
            controller.isAppearanceLightNavigationBars = false
        }

        // Tratar intent inicial
        handleIntent(intent)
    }
    
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }
    
    private fun handleIntent(intent: Intent) {
        val appLinkAction = intent.action
        val appLinkData: Uri? = intent.data
        
        if (Intent.ACTION_VIEW == appLinkAction && appLinkData != null) {
            // Configurar MethodChannel para comunicar com Flutter
            flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                val channel = MethodChannel(messenger, CHANNEL)
                
                when {
                    appLinkData.path?.startsWith("/product") == true -> {
                        val productId = appLinkData.getQueryParameter("id")
                        channel.invokeMethod("navigateToProduct", mapOf("id" to productId))
                    }
                    appLinkData.path?.startsWith("/category") == true -> {
                        val categoryId = appLinkData.getQueryParameter("id")
                        channel.invokeMethod("navigateToCategory", mapOf("id" to categoryId))
                    }
                }
            }
        }
    }
}
